#!/usr/bin/env python3
"""
Test script to verify battle system fixes
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.pokemon.models import BattleFormat, BattlePlayer, Team, PokemonSet
from plugins.pokemon.showdown_bridge.bridge import ShowdownBridge
from plugins.pokemon.battle.battle_manager import BattleManager

async def test_random_battle_creation():
    """Test creating a random battle"""
    print("Testing random battle creation...")
    
    # Initialize bridge
    bridge = ShowdownBridge()
    await bridge.initialize()
    
    # Create dummy teams (not needed for random battles)
    dummy_team = Team(
        name="Test Team",
        pokemon=[],
        format=BattleFormat.GEN9RANDOMBATTLE,
        owner_id="test"
    )
    
    # Create battle
    battle = await bridge.create_battle(
        format=BattleFormat.GEN9RANDOMBATTLE,
        player1_team=dummy_team,
        player1_username="Player1",
        player2_team=dummy_team,
        player2_username="Player2"
    )
    
    if battle:
        print(f"✓ Battle created successfully: {battle.id}")
        print(f"  Format: {battle.format}")
        print(f"  Status: {battle.status}")
        print(f"  Players: {list(battle.players.keys())}")
        
        # Wait a bit to see if status changes
        await asyncio.sleep(2)
        print(f"  Status after 2s: {battle.status}")
        
        return battle
    else:
        print("✗ Failed to create battle")
        return None

async def test_forfeit_functionality(battle):
    """Test forfeit functionality"""
    if not battle:
        print("Skipping forfeit test - no battle available")
        return
    
    print("\nTesting forfeit functionality...")
    
    # Initialize battle manager
    manager = BattleManager()
    await manager.initialize()
    
    # Try to forfeit
    success = await manager.forfeit_battle(battle.id, "test_user")
    
    if success:
        print("✓ Forfeit command sent successfully")
    else:
        print("✗ Forfeit command failed")

async def main():
    """Main test function"""
    print("Starting battle system tests...\n")
    
    try:
        # Test 1: Random battle creation
        battle = await test_random_battle_creation()
        
        # Test 2: Forfeit functionality
        await test_forfeit_functionality(battle)
        
        print("\nTests completed!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
