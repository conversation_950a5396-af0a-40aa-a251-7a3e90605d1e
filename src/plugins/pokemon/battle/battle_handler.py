"""
Battle Handler

Handles individual battle instances and manages battle state.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import uuid

from ..models import Battle, BattleStatus, BattleRequest, BattleAction, BattlePlayer
from ..showdown_bridge import ShowdownBridge
from ..showdown_bridge.message_parser import ParsedMessage, MessageType
from ..config import config

from nonebot import logger


class BattleHandler:
    """Handles a single battle instance"""
    
    def __init__(self, battle: Battle, showdown_bridge: ShowdownBridge):
        self.battle = battle
        self.showdown_bridge = showdown_bridge
        self.callbacks: List[Callable] = []
        self.pending_requests: Dict[str, BattleRequest] = {}  # player_id -> request
        self.action_queue: List[BattleAction] = []
        self.last_activity = datetime.now()
        self._lock = asyncio.Lock()
        
        # Setup battle callback
        self.showdown_bridge.add_battle_callback(battle.id, self._handle_battle_message)
    
    async def start_battle(self) -> bool:
        """Start the battle"""
        try:
            async with self._lock:
                if self.battle.status != BattleStatus.WAITING:
                    logger.warning(f"Battle {self.battle.id} is not in waiting status")
                    return False

                # Battle should already be created in Showdown by the bridge
                # Don't manually set status - let Showdown messages determine the state
                self.battle.started_at = datetime.now()
                self.last_activity = datetime.now()

                logger.info(f"Started battle {self.battle.id}")
                await self._notify_callbacks("battle_started", {"battle": self.battle})

                return True

        except Exception as e:
            logger.error(f"Error starting battle {self.battle.id}: {e}")
            return False
    
    async def submit_action(self, player_id: str, action: str) -> bool:
        """Submit a battle action"""
        try:
            async with self._lock:
                if self.battle.status not in [BattleStatus.TEAM_PREVIEW, BattleStatus.ACTIVE]:
                    logger.warning(f"Battle {self.battle.id} is not accepting actions (status: {self.battle.status})")
                    return False

                if player_id not in self.battle.players:
                    logger.warning(f"Player {player_id} not in battle {self.battle.id}")
                    return False

                # Validate action based on battle status
                if self.battle.status == BattleStatus.TEAM_PREVIEW:
                    if not self._validate_team_preview_action(action):
                        logger.warning(f"Invalid team preview action: {action}")
                        return False

                # Create action record
                battle_action = BattleAction(
                    battle_id=self.battle.id,
                    player_id=player_id,
                    action_type="action",
                    action_data=action
                )

                self.action_queue.append(battle_action)

                # Send to Showdown
                success = await self.showdown_bridge.send_battle_action(
                    self.battle.id, player_id, action
                )

                if success:
                    self.last_activity = datetime.now()
                    logger.debug(f"Submitted action for battle {self.battle.id}, player {player_id}: {action}")

                    # Clear pending request for this player
                    self.pending_requests.pop(player_id, None)

                    await self._notify_callbacks("action_submitted", {
                        "battle": self.battle,
                        "player_id": player_id,
                        "action": action
                    })

                return success

        except Exception as e:
            logger.error(f"Error submitting action for battle {self.battle.id}: {e}")
            return False

    def _validate_team_preview_action(self, action: str) -> bool:
        """Validate team preview action"""
        # Team preview actions should be in format "team X" where X is 1-6
        parts = action.strip().split()
        if len(parts) != 2:
            return False

        if parts[0].lower() != "team":
            return False

        try:
            team_pos = int(parts[1])
            return 1 <= team_pos <= 6
        except ValueError:
            return False
    
    async def _handle_battle_message(self, parsed_message: ParsedMessage):
        """Handle a message from Pokemon Showdown"""
        try:
            async with self._lock:
                self.last_activity = datetime.now()
                
                if parsed_message.message_type == MessageType.UPDATE:
                    await self._handle_battle_update(parsed_message)
                elif parsed_message.message_type == MessageType.SIDEUPDATE:
                    await self._handle_side_update(parsed_message)
                elif parsed_message.message_type == MessageType.END:
                    await self._handle_battle_end(parsed_message)
                elif parsed_message.message_type == MessageType.ERROR:
                    await self._handle_battle_error(parsed_message)
                
                # Notify callbacks
                await self._notify_callbacks("message_received", {
                    "battle": self.battle,
                    "message": parsed_message
                })
                
        except Exception as e:
            logger.error(f"Error handling battle message: {e}")
    
    async def _handle_battle_update(self, parsed_message: ParsedMessage):
        """Handle battle update message"""
        events = parsed_message.data.get("events", [])
        
        for event in events:
            event_type = event.get("type")
            
            if event_type == "start":
                self.battle.status = BattleStatus.ACTIVE
                await self._notify_callbacks("battle_active", {"battle": self.battle})
                
            elif event_type == "turn":
                turn_number = event.get("turn_number")
                if turn_number:
                    self.battle.current_turn = turn_number
                    await self._notify_callbacks("turn_start", {
                        "battle": self.battle,
                        "turn": turn_number
                    })
                    
            elif event_type == "win":
                winner = event.get("winner")
                if winner:
                    self.battle.winner = winner
                    self.battle.status = BattleStatus.FINISHED
                    self.battle.finished_at = datetime.now()
                    await self._notify_callbacks("battle_finished", {
                        "battle": self.battle,
                        "winner": winner
                    })
                    
            elif event_type == "move":
                await self._notify_callbacks("pokemon_move", {
                    "battle": self.battle,
                    "event": event
                })
                
            elif event_type == "switch":
                await self._notify_callbacks("pokemon_switch", {
                    "battle": self.battle,
                    "event": event
                })
                
            elif event_type == "damage":
                await self._notify_callbacks("pokemon_damage", {
                    "battle": self.battle,
                    "event": event
                })
                
            elif event_type == "faint":
                await self._notify_callbacks("pokemon_faint", {
                    "battle": self.battle,
                    "event": event
                })
    
    async def _handle_side_update(self, parsed_message: ParsedMessage):
        """Handle side update message (player-specific)"""
        player_id = parsed_message.player_id
        request_data = parsed_message.data.get("request")

        if request_data and player_id:
            # Determine request type
            request_type = "unknown"
            if "teamPreview" in request_data:
                request_type = "teampreview"
                # Update battle status to team preview if not already set
                if self.battle.status == BattleStatus.WAITING:
                    self.battle.status = BattleStatus.TEAM_PREVIEW
                    await self._notify_callbacks("team_preview_started", {"battle": self.battle})
            elif "active" in request_data:
                request_type = "move"
                # Update battle status to active if not already set
                if self.battle.status != BattleStatus.ACTIVE:
                    self.battle.status = BattleStatus.ACTIVE
                    await self._notify_callbacks("battle_active", {"battle": self.battle})
            elif "forceSwitch" in request_data:
                request_type = "switch"

            # Create a battle request
            battle_request = BattleRequest(
                battle_id=self.battle.id,
                player_id=player_id,
                request_type=request_type,
                data=request_data
            )

            self.pending_requests[player_id] = battle_request
            
            await self._notify_callbacks("action_required", {
                "battle": self.battle,
                "player_id": player_id,
                "request": battle_request
            })
    
    async def _handle_battle_end(self, parsed_message: ParsedMessage):
        """Handle battle end message"""
        if self.battle.status != BattleStatus.FINISHED:
            self.battle.status = BattleStatus.FINISHED
            self.battle.finished_at = datetime.now()
            
            await self._notify_callbacks("battle_ended", {
                "battle": self.battle,
                "end_data": parsed_message.data
            })
    
    async def _handle_battle_error(self, parsed_message: ParsedMessage):
        """Handle battle error message"""
        error_msg = parsed_message.data.get("error", "Unknown error")
        logger.error(f"Battle {self.battle.id} error: {error_msg}")
        
        self.battle.status = BattleStatus.ERROR
        
        await self._notify_callbacks("battle_error", {
            "battle": self.battle,
            "error": error_msg
        })
    
    def add_callback(self, callback: Callable):
        """Add a callback for battle events"""
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """Remove a battle callback"""
        try:
            self.callbacks.remove(callback)
        except ValueError:
            pass
    
    async def _notify_callbacks(self, event_type: str, data: Dict[str, Any]):
        """Notify all callbacks of an event"""
        for callback in self.callbacks:
            try:
                await callback(event_type, data)
            except Exception as e:
                logger.error(f"Error in battle callback: {e}")
    
    def get_pending_request(self, player_id: str) -> Optional[BattleRequest]:
        """Get pending request for a player"""
        return self.pending_requests.get(player_id)
    
    def get_all_pending_requests(self) -> Dict[str, BattleRequest]:
        """Get all pending requests"""
        return self.pending_requests.copy()
    
    def is_active(self) -> bool:
        """Check if battle is active"""
        return self.battle.status in [BattleStatus.TEAM_PREVIEW, BattleStatus.ACTIVE]
    
    def is_finished(self) -> bool:
        """Check if battle is finished"""
        return self.battle.status in [BattleStatus.FINISHED, BattleStatus.CANCELLED, BattleStatus.ERROR]
    
    def is_timed_out(self) -> bool:
        """Check if battle has timed out"""
        if self.is_finished():
            return False
        
        timeout_duration = timedelta(seconds=config.battle_timeout)
        return datetime.now() - self.last_activity > timeout_duration
    
    async def forfeit(self, player_id: str) -> bool:
        """Forfeit the battle for a player"""
        try:
            if player_id not in self.battle.players:
                return False
            
            # Send forfeit command to Showdown
            success = await self.showdown_bridge.send_battle_action(
                self.battle.id, player_id, "/forfeit"
            )
            
            if success:
                # Determine winner (the other player)
                other_player = "p2" if player_id == "p1" else "p1"
                self.battle.winner = other_player
                self.battle.status = BattleStatus.FINISHED
                self.battle.finished_at = datetime.now()
                
                await self._notify_callbacks("battle_forfeited", {
                    "battle": self.battle,
                    "forfeiter": player_id,
                    "winner": other_player
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error forfeiting battle {self.battle.id}: {e}")
            return False
    
    async def cancel(self, reason: str = "Cancelled") -> bool:
        """Cancel the battle"""
        try:
            self.battle.status = BattleStatus.CANCELLED
            self.battle.finished_at = datetime.now()
            
            await self._notify_callbacks("battle_cancelled", {
                "battle": self.battle,
                "reason": reason
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling battle {self.battle.id}: {e}")
            return False
    
    def get_battle_summary(self) -> Dict[str, Any]:
        """Get a summary of the battle"""
        summary = {
            "id": self.battle.id,
            "format": self.battle.format.value,
            "status": self.battle.status.value,
            "players": {
                pid: {
                    "username": player.username,
                    "ready": player.ready
                }
                for pid, player in self.battle.players.items()
            },
            "current_turn": self.battle.current_turn,
            "winner": self.battle.winner,
            "created_at": self.battle.created_at.isoformat(),
            "started_at": self.battle.started_at.isoformat() if self.battle.started_at else None,
            "finished_at": self.battle.finished_at.isoformat() if self.battle.finished_at else None,
            "last_activity": self.last_activity.isoformat(),
            "pending_requests": len(self.pending_requests),
            "total_actions": len(self.action_queue)
        }

        # Add team preview info if in that state
        if self.battle.status == BattleStatus.TEAM_PREVIEW:
            summary["team_preview"] = self.get_team_preview_info()

        return summary

    def get_team_preview_info(self) -> Dict[str, Any]:
        """Get team preview information"""
        team_info = {}

        for player_id, player in self.battle.players.items():
            if player.team:
                team_info[player_id] = [
                    {
                        "species": pokemon.species,
                        "level": pokemon.level,
                        "gender": pokemon.gender,
                        "item": pokemon.item if pokemon.item else None
                    }
                    for pokemon in player.team.pokemon
                ]

        return {
            "teams": team_info,
            "instructions": "选择你的首发宝可梦 (1-6)",
            "ready_players": [pid for pid, req in self.pending_requests.items() if req.request_type == "teampreview"]
        }
    
    async def cleanup(self):
        """Clean up the battle handler"""
        try:
            # Remove callback from showdown bridge
            self.showdown_bridge.remove_battle_callback(self.battle.id, self._handle_battle_message)
            
            # Clear callbacks
            self.callbacks.clear()
            
            logger.debug(f"Cleaned up battle handler for {self.battle.id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up battle handler: {e}")
